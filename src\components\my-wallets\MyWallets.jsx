"use client";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import Cookies from "js-cookie";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import { Modal } from "../ui/modal";
import { useModal } from "@/hooks/useModal";

const MyAllWallets = () => {
  const {
    isOpen: isDeleteModalOpen,
    openModal: openDeleteModal,
    closeModal: closeDeleteModal,
  } = useModal();

  // use network
  const networkService = new NetworkService();

  // state
  const [walletsCardsData, setWalletsCardsData] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedWalletToDelete, setSelectedWalletToDelete] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch all wallets
  const fetchAllWallets = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(ApiPath.allWallets);
      setWalletsCardsData(res.data.data);
    } finally {
      setLoading(false);
    }
  };

  // Fetch all currencies
  const fetchCurrencies = async () => {
    try {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-currencies`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setCurrencies(data.data);
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to fetch currencies"
      );
    }
  };

  // Filter only currencies user doesn't already have
  const getAvailableCurrencies = () => {
    const existingCodes = walletsCardsData?.wallets.map((w) => w.code);
    return currencies.filter((c) => !existingCodes.includes(c.code));
  };

  // card style
  const cardStyles = [
    {
      gradient: "from-indigo-600 via-purple-600 to-purple-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-blue-600 via-indigo-600 to-purple-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-cyan-500 via-blue-500 to-indigo-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-violet-600 via-purple-600 to-indigo-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-emerald-500 via-teal-600 to-cyan-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
  ];

  // Create a wallet
  const createWallet = async () => {
    try {
      setLoading(true);
      const res = await networkService.post(ApiPath.createWallet, {
        currency_id: selectedCurrency,
      });
      if (res.status === "completed") {
        toast.success(res.data.message);
        fetchAllWallets();
        closeModal();
      }
    } finally {
      setLoading(false);
    }
  };

  // Delete Wallet
  const handleDeleteWallet = async (walletId) => {
    try {
      const res = await networkService.delete(
        `${ApiPath.deleteWallet}${walletId}`
      );
      await networkService.delete(`${ApiPath.deleteWallet}${walletId}`);
      if (res.status === "completed") {
        toast.success(res.data.message);
      }
      fetchAllWallets();
    } finally {
    }
  };

  const openModal = () => {
    if (getAvailableCurrencies().length === 0) {
      toast.info("You already have wallets for all available currencies");
      return;
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCurrency("");
  };

  const handleOpenDeleteModal = (wallet) => {
    setSelectedWalletToDelete(wallet);
    openDeleteModal();
  };

  const handleCloseDeleteModal = () => {
    closeDeleteModal();
    setSelectedWalletToDelete(null);
  };

  const confirmDeleteWallet = async () => {
    if (selectedWalletToDelete) {
      await handleDeleteWallet(selectedWalletToDelete.id);
      handleCloseDeleteModal();
    }
  };

  useEffect(() => {
    fetchCurrencies();
    fetchAllWallets();
  }, []);

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Wallets
          </h1>
          <button
            onClick={openModal}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Add New Wallet
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Wallet Overview
            </h2>
          </div>
          <div className="p-6">
            {loading ? (
              <p className="text-gray-500 dark:text-gray-400 text-center">
                Loading wallets...
              </p>
            ) : walletsCardsData?.wallets?.length > 0 ? (
              <div className="flex gap-6 w-full overflow-x-auto pb-4">
                {walletsCardsData?.wallets?.map((wallet, i) => {
                  const style = cardStyles[i % cardStyles.length];

                  return (
                    <div
                      key={wallet.id}
                      className={`bg-gradient-to-br ${style.gradient} 
                rounded-3xl min-w-[300px] p-6 text-white shadow-xl relative overflow-hidden
                backdrop-blur-xl`}
                    >
                      {/* Background Pattern */}
                      <div className="absolute inset-0">
                        <div className="absolute top-0 right-0 w-40 h-40 bg-white/5 rounded-full translate-x-16 -translate-y-16"></div>
                        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full -translate-x-10 translate-y-10"></div>
                      </div>

                      {/* Content */}
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center gap-4">
                            <div
                              className={`w-12 h-12 ${style.iconBg} rounded-2xl flex items-center justify-center backdrop-blur-sm shadow-lg`}
                            >
                              {wallet.icon && wallet.icon !== "null" ? (
                                <img
                                  src={wallet.icon}
                                  alt={wallet.name}
                                  className="w-7 h-7"
                                />
                              ) : (
                                <span className="font-bold text-lg">
                                  {wallet.symbol}
                                </span>
                              )}
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg leading-tight">
                                {wallet.name}
                              </h3>
                              <p className="text-white/70 text-sm font-medium">
                                {wallet.code}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Balance Section */}
                        <div className="mb-6">
                          <p className="text-white/70 text-sm font-medium mb-2">
                            Current Balance
                          </p>
                          <h2 className="text-2xl font-bold leading-tight mb-1">
                            {wallet.formatted_balance}
                          </h2>
                          <p className="text-white/80 text-lg font-medium">
                            {wallet.code}
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-3">
                          <button
                            className={`${style.accent} px-0 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                          >
                            <span className="flex items-center justify-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M12 4v16m8-8H4"
                                />
                              </svg>
                              Top Up
                            </span>
                          </button>
                          <button
                            className={`${style.accent} px-3 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                          >
                            <span className="flex items-center justify-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M20 12H4"
                                />
                              </svg>
                              Withdraw
                            </span>
                          </button>
                        </div>
                      </div>

                      <button
                        onClick={() => handleOpenDeleteModal(wallet)}
                        type="button"
                        className="absolute top-4 right-4 p-2 z-10 rounded-full bg-white/10 hover:bg-white/20"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="size-6"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                          />
                        </svg>
                      </button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400 text-center">
                  No wallets found. Create your first wallet to get started.
                </p>
              </div>
            )}
          </div>
        </div>
        {/* Create Wallet Modal */}
        {showModal && (
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex justify-center items-center z-50 px-4"
            onClick={(e) => e.target === e.currentTarget && closeModal()}
          >
            <div className="bg-white rounded-3xl w-full max-w-md shadow-2xl animate-[slideUp_0.3s_ease-out]">
              <div className="p-8">
                {/* Modal Header */}
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                    <svg
                      className="w-8 h-8 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Create New Wallet
                  </h2>
                  <p className="text-gray-600">
                    Choose a currency for your new wallet
                  </p>
                </div>

                {/* Currency Select */}
                <div className="mb-8">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Select Currency
                  </label>
                  <div className="relative">
                    <select
                      className="w-full py-2 px-2 border-2 border-gray-200 rounded-2xl bg-white text-gray-900 font-medium focus:border-indigo-500 focus:outline-none transition-colors duration-200 appearance-none cursor-pointer hover:border-gray-300"
                      value={selectedCurrency}
                      onChange={(e) => setSelectedCurrency(e.target.value)}
                      disabled={loading}
                    >
                      <option value="" disabled>
                        Choose a currency...
                      </option>
                      {getAvailableCurrencies().map((c) => (
                        <option key={c.id} value={c.id}>
                          {c.name} ({c.code})
                        </option>
                      ))}
                    </select>
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg
                        className="w-5 h-5 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <button
                    onClick={closeModal}
                    disabled={loading}
                    className="flex-1 h-[50px] px-6 border-2 border-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 hover:border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={createWallet}
                    disabled={loading || !selectedCurrency}
                    className="flex-1 h-[50px] px-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                  >
                    <span className="flex items-center justify-center gap-2">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                      Create Wallet
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          className="max-w-md m-4"
          showCloseButton={false}
        >
          <div className="p-8">
            {/* Modal Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Delete Wallet
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this wallet?
              </p>
            </div>

            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl">
              <p className="text-red-800 dark:text-red-400 text-sm font-medium">
                ⚠️ This action cannot be undone. All transaction history for
                this wallet will be permanently deleted.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <button
                onClick={handleCloseDeleteModal}
                disabled={loading}
                className="flex-1 h-[50px] px-6 border-2 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteWallet}
                disabled={loading}
                className="flex-1 h-[50px] px-6 bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold rounded-lg hover:from-red-600 hover:to-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
              >
                <span className="flex items-center justify-center gap-2">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Delete Wallet
                </span>
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default MyAllWallets;
