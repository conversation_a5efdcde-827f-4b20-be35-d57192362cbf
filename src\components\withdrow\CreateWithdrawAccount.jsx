"use client";
import React, { useEffect, useState } from "react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import NetworkService from "@/network/service/network_service";
import ApiPath from "@/network/api/api_path";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";

function CreateWithdrawAccount() {
  // use network
  const networkService = new NetworkService();

  const [walletData, setWalletData] = useState([]);
  const [withdrawMethod, setWithdrawMethod] = useState([]);

  const [selectedWallet, setSelectedWallet] = useState("");
  const [selectedMethod, setSelectedMethod] = useState("");

  const router = useRouter();

  // Fetch wallet data
  const fetchWalletData = async () => {
    try {
      const res = await networkService.get(ApiPath.allWallets);
      if (res.status === "completed") {
        setWalletData(res.data.data.wallets);
      }
    } finally {
    }
  };

  // Handle wallet change
  const handleWalletChange = async (e) => {
    const value = e.target.value;
    setSelectedWallet(value);
    const findWalletCode = walletData.find((w) => w.id === Number(value));
    await fetchWithdrawMethods(
      findWalletCode?.id === 0 ? "default" : findWalletCode?.id
    );
  };

  // Fetch withdraw methods
  const fetchWithdrawMethods = async (walletCode) => {
    try {
      const res = await networkService.get(
        `${ApiPath.withdrawMethods}?currency=${walletCode}`
      );
      if (res.status === "completed") {
        setWithdrawMethod(res.data.data);
      }
    } finally {
    }
  };

  useEffect(() => {
    fetchWalletData();
  }, []);

  // Handle form submission
  const handlePostSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();

    formData.append("wallet_id", selectedWallet);
    formData.append("withdraw_method_id", selectedMethod);
    formData.append(
      "method_name",
      withdrawMethod.find((m) => m.id === Number(selectedMethod)).name
    );

    const selectedMethodFields = withdrawMethod.find(
      (m) => m.id === Number(selectedMethod)
    )?.fields;

    selectedMethodFields.forEach((field) => {
      const fieldName = field.name;
      const input = e.target.elements[fieldName];

      if (!input) return;

      if (field.type === "file") {
        formData.append(`credentials[${fieldName}][type]`, field.type);
        formData.append(
          `credentials[${fieldName}][validation]`,
          field.validation
        );
        if (input.files[0]) {
          formData.append(`credentials[${fieldName}][value]`, input.files[0]);
        }
      } else {
        formData.append(`credentials[${fieldName}][type]`, field.type);
        formData.append(
          `credentials[${fieldName}][validation]`,
          field.validation
        );
        formData.append(`credentials[${fieldName}][value]`, input.value);
      }
    });

    try {
      const res = await networkService.postFormData(
        ApiPath.createWithdrawAccount,
        formData
      );
      if (res.status === "completed") {
        toast.success(res.data.message);
        router.push("/withdraw/withdraw-account");
      }
    } catch (error) {
      toast.error("Something went wrong.");
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Form */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Link
              href="/withdraw/withdraw-account"
              className="inline-flex items-center p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Withdrawal Accounts
            </h2>
          </div>
        </div>
        <div className="grid grid-cols-12">
          <div className="col-span-6">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Add Withdrawal Account
                </h2>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  Add a new bank account or wallet address for withdrawals.
                </p>
              </div>
              <div className="p-6">
                <form onSubmit={handlePostSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Select Wallet
                    </label>
                    <select
                      value={selectedWallet}
                      onChange={handleWalletChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select a wallet</option>
                      {walletData.map((wallet) => (
                        <option key={wallet.id} value={wallet.id}>
                          {wallet.name} {wallet.code}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Withdraw Method
                    </label>
                    <select
                      value={selectedMethod}
                      onChange={(e) => setSelectedMethod(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select a method</option>
                      {withdrawMethod.map((method) => (
                        <option key={method.id} value={method.id}>
                          {method.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Dynamic Fields */}
                  {selectedMethod && (
                    <div className="space-y-4 mt-4">
                      {withdrawMethod
                        .find((m) => m.id === Number(selectedMethod))
                        ?.fields?.map((field, idx) => (
                          <div key={idx}>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              {field.name}
                            </label>

                            {field.type === "text" && (
                              <input
                                type="text"
                                name={field.name}
                                required={field.validation === "required"}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              />
                            )}

                            {field.type === "textarea" && (
                              <textarea
                                name={field.name}
                                required={field.validation === "required"}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              />
                            )}

                            {field.type === "file" && (
                              <input
                                type="file"
                                name={field.name}
                                required={field.validation === "required"}
                                className="mt-1 block w-full text-gray-700 border p-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              />
                            )}
                          </div>
                        ))}
                    </div>
                  )}

                  <button
                    type="submit"
                    className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Add Account
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateWithdrawAccount;
